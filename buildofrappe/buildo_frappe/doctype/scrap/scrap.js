// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Scrap", {
	refresh(frm) {
		// Calculate total_value when form loads
		calculate_total_value(frm);
	},

	qty_sold(frm) {
		// Calculate total_value when qty_sold changes
		calculate_total_value(frm);
	},

	rate(frm) {
		// Calculate total_value when rate changes
		calculate_total_value(frm);
	}
});

function calculate_total_value(frm) {
	// Calculate total_value = qty_sold * rate
	if (frm.doc.qty_sold && frm.doc.rate) {
		frm.set_value('total_value', frm.doc.qty_sold * frm.doc.rate);
	} else {
		frm.set_value('total_value', 0);
	}
}
