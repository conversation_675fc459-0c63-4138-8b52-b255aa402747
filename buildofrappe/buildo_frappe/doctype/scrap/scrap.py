# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

# import frappe
from frappe.model.document import Document


class Scrap(Document):
	def validate(self):
		"""Calculate total_value before saving the document"""
		self.calculate_total_value()

	def calculate_total_value(self):
		"""Calculate total_value = qty_sold * rate"""
		if self.qty_sold and self.rate:
			self.total_value = self.qty_sold * self.rate
		else:
			self.total_value = 0
