# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def execute(filters=None):
	"""Main function to execute the Scrap Report"""
	columns = get_columns()
	data = get_data(filters)
	return columns, data


def get_columns():
	"""Define the columns for the report"""
	return [
		{
			"fieldname": "name",
			"label": _("Scrap ID"),
			"fieldtype": "Link",
			"options": "Scrap",
			"width": 200
		},
		{
			"fieldname": "date",
			"label": _("Date"),
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "project",
			"label": _("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		},
		{
			"fieldname": "vendor_name",
			"label": _("Vendor"),
			"fieldtype": "Data",
			"width": 150
		},
		{
			"fieldname": "sold_by",
			"label": _("Sold By"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "qty_sold",
			"label": _("Qty Sold (MT)"),
			"fieldtype": "Float",
			"width": 120
		},
		{
			"fieldname": "rate",
			"label": _("Rate"),
			"fieldtype": "Float",
			"width": 100
		},
		{
			"fieldname": "total_value",
			"label": _("Total Value"),
			"fieldtype": "Float",
			"width": 120
		}
	]


def get_conditions(filters):
	"""Build conditions and values for the SQL query based on filters"""
	conditions = []
	values = {}

	# Project filter
	if filters.get("project"):
		conditions.append("project = %(project)s")
		values["project"] = filters.get("project")

	# Vendor filter
	if filters.get("vendor_name"):
		conditions.append("vendor_name LIKE %(vendor_name)s")
		values["vendor_name"] = f"%{filters.get('vendor_name')}%"

	# Sold By filter
	if filters.get("sold_by"):
		conditions.append("sold_by LIKE %(sold_by)s")
		values["sold_by"] = f"%{filters.get('sold_by')}%"

	# Date range filters
	if filters.get("from_date"):
		conditions.append("date >= %(from_date)s")
		values["from_date"] = filters.get("from_date")

	if filters.get("to_date"):
		conditions.append("date <= %(to_date)s")
		values["to_date"] = filters.get("to_date")

	return conditions, values


def get_data(filters):
	"""Get the data for the report based on filters"""
	conditions, values = get_conditions(filters)

	# Build the WHERE clause
	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	# Execute the query
	query = f"""
		SELECT
			name,
			date,
			project,
			vendor_name,
			sold_by,
			qty_sold,
			rate,
			total_value
		FROM `tabScrap`
		{where_clause}
		ORDER BY date DESC, name DESC
	"""

	try:
		data = frappe.db.sql(query, values, as_dict=True)
		return data
	except Exception as e:
		frappe.log_error(f"Error in Scrap Report query: {str(e)}")
		frappe.throw(_("Error fetching data. Please check the filters and try again."))
