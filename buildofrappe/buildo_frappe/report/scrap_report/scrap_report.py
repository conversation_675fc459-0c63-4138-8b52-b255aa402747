# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def execute(filters=None):
	"""Main function to execute the Scrap Report"""
	columns = get_columns()
	data = get_data(filters)

	# Add total row
	if data:
		total_row = get_total_row(data)
		data.append(total_row)

	return columns, data


def get_columns():
	"""Define the columns for the report"""
	return [
		{
			"fieldname": "name",
			"label": _("Scrap ID"),
			"fieldtype": "Link",
			"options": "Scrap",
			"width": 200
		},
		{
			"fieldname": "date",
			"label": _("Date"),
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "project",
			"label": _("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		},
		{
			"fieldname": "vendor_name",
			"label": _("Vendor"),
			"fieldtype": "Data",
			"width": 150
		},
		{
			"fieldname": "sold_by",
			"label": _("Sold By"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "rate",
			"label": _("Rate"),
			"fieldtype": "Float",
			"width": 100
		},
		{
			"fieldname": "qty_sold",
			"label": _("Qty Sold (Kgs)"),
			"fieldtype": "Float",
			"width": 150
		},
		{
			"fieldname": "total_value",
			"label": _("Total Value"),
			"fieldtype": "Float",
			"width": 120
		}
	]


def get_conditions(filters):
	"""Build conditions and values for the SQL query based on filters"""
	conditions = []
	values = {}

	# Project filter
	if filters.get("project"):
		conditions.append("project = %(project)s")
		values["project"] = filters.get("project")

	# Vendor filter
	if filters.get("vendor_name"):
		conditions.append("vendor_name = %(vendor_name)s")
		values["vendor_name"] = filters.get("vendor_name")

	# Sold By filter
	if filters.get("sold_by"):
		conditions.append("sold_by LIKE %(sold_by)s")
		values["sold_by"] = f"%{filters.get('sold_by')}%"

	# Date range filters
	if filters.get("from_date"):
		conditions.append("date >= %(from_date)s")
		values["from_date"] = filters.get("from_date")

	if filters.get("to_date"):
		conditions.append("date <= %(to_date)s")
		values["to_date"] = filters.get("to_date")

	return conditions, values


def get_data(filters):
	"""Get the data for the report based on filters"""
	conditions, values = get_conditions(filters)

	# Build the WHERE clause
	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	# Execute the query
	query = f"""
		SELECT
			name,
			date,
			project,
			vendor_name,
			sold_by,
			rate,
			qty_sold,
			total_value
		FROM `tabScrap`
		{where_clause}
		ORDER BY date DESC, name DESC
	"""

	try:
		data = frappe.db.sql(query, values, as_dict=True)
		return data
	except Exception as e:
		frappe.log_error(f"Error in Scrap Report query: {str(e)}")
		frappe.throw(_("Error fetching data. Please check the filters and try again."))


@frappe.whitelist()
def get_vendors():
	"""Get list of unique vendors from Scrap records"""
	try:
		vendors = frappe.db.sql("""
			SELECT DISTINCT vendor_name
			FROM `tabScrap`
			WHERE vendor_name IS NOT NULL AND vendor_name != ''
			ORDER BY vendor_name
		""", as_dict=True)

		# Format for Select field options - return as newline separated string
		vendor_options = ["\n"]  # Start with empty option
		for vendor in vendors:
			if vendor.vendor_name:
				vendor_options.append(vendor.vendor_name)

		return "\n".join(vendor_options)
	except Exception as e:
		frappe.log_error(f"Error fetching vendors: {str(e)}")
		return "\n"
