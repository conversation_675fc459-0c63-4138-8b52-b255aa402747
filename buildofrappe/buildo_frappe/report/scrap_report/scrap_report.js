// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Scrap Report"] = {
	"filters": [
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"reqd": 0
		},
		{
			"fieldname": "vendor_name",
			"label": __("Vendor"),
			"fieldtype": "Data",
			"reqd": 0
		},
		{
			"fieldname": "sold_by",
			"label": __("Sold By"),
			"fieldtype": "Data",
			"reqd": 0
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"reqd": 0
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"reqd": 0
		}
	]
};
