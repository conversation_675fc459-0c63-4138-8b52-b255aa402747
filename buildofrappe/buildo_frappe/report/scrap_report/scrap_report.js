// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Scrap Report"] = {
	"filters": [
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"reqd": 0
		},
		{
			"fieldname": "vendor_name",
			"label": __("Vendor"),
			"fieldtype": "Select",
			"reqd": 0
		},
		{
			"fieldname": "sold_by",
			"label": __("Sold By"),
			"fieldtype": "Data",
			"reqd": 0
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"reqd": 0
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"reqd": 0
		}
	],

	"onload": function(report) {
		// Load vendor options dynamically
		frappe.call({
			method: "buildofrappe.buildo_frappe.report.scrap_report.scrap_report.get_vendors",
			callback: function(r) {
				if (r.message) {
					var vendor_filter = report.page.fields_dict.vendor_name;
					if (vendor_filter) {
						vendor_filter.df.options = r.message;
						vendor_filter.refresh();
					}
				}
			}
		});
	}
};
